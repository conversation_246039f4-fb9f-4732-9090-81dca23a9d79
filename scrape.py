import os
from crawl4ai import Crawl4ai

# 目标URL
URL = "https://m.yicai.com/news/gushi/"

# 定义输出文件的路径
# 我们将结果保存在 output 文件夹中，这个文件夹会被映射到我们本地的电脑上
output_dir = "output"
os.makedirs(output_dir, exist_ok=True)
output_file_path = os.path.join(output_dir, "yicai_gushi_news.json")

# 定义一个AI提示（Prompt），告诉爬虫我们想要什么信息
# 这是 crawl4ai 的核心功能，使用自然语言定义抓取目标
PROMPT = f"""
请从网址 {URL} 爬取以下信息:
1.  所有新闻的标题 (title)。
2.  每条新闻对应的详情页链接 (url)。
3.  每条新闻的发布日期或时间 (date)。

请将抓取到的所有新闻条目整理成一个JSON数组格式，每个条目都是一个包含 'title', 'url', 和 'date' 键的对象。
"""

print(f"[*] 正在初始化爬虫，目标网址: {URL}")

# 初始化 Crawl4ai
# 使用 aio=False 可以在简单的脚本中以同步方式运行
# headless=True 表示在后台运行浏览器，不弹出浏览器窗口
crawler = Crawl4ai(aio=False, headless=True)

print("[*] 开始执行爬取任务...")

# 运行爬虫
# .run() 方法会访问URL，并让AI根据你的PROMPT来提取数据
result = crawler.run(url=URL, prompt=PROMPT)

print("[*] 爬取完成！")

# 检查是否有内容并保存到文件
if result and result.content:
    print(f"[*] 成功提取内容，正在保存到文件: {output_file_path}")
    # 将提取到的内容写入JSON文件
    with open(output_file_path, 'w', encoding='utf-8') as f:
        f.write(result.content)
    print("[*] 文件保存成功！")
else:
    print("[!] 未能提取到任何内容，请检查网站结构或Prompt。")