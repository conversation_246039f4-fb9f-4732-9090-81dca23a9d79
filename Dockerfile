# 1. 使用官方的 Python 3.11-slim 作为基础镜像
# slim版本体积更小，适合部署
FROM python:3.11-slim

# 2. 设置工作目录
# 容器内所有后续操作都将在此目录下进行
WORKDIR /app

# 3. 复制依赖文件（如果有时）到工作目录
# 我们这里直接安装，所以此步骤可以省略，但保留是个好习惯
# COPY requirements.txt .

# 4. 安装 crawl4ai 及其 Playwright 依赖
# crawl4ai 使用 Playwright 来渲染动态JavaScript页面，这对于现代网站至关重要
# 我们使用清华大学的 PyPI 镜像源来加速下载
RUN apt-get update

RUN pip install "crawl4ai[playwright]" && \
    playwright install --with-deps chromium

# 5. 复制项目文件到工作目录
# 将当前目录下的所有文件（.py, .yml等）复制到容器的 /app 目录
COPY . .

# 6. 设置容器启动时要执行的默认命令
# 当容器启动时，它会自动运行 scrape.py 脚本
CMD ["python", "scrape.py"]